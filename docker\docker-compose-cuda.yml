version: '3.8'
services:
  mast3r-demo:
    build:
      context: ./files
      dockerfile: cuda.Dockerfile 
    ports:
      - "7860:7860"
    environment:
      - DEVICE=cuda
      - MODEL=${MODEL:-MASt3R_ViTLarge_BaseDecoder_512_catmlpdpt_metric.pth}
    volumes:
      - ./files/checkpoints:/mast3r/checkpoints
    cap_add:
      - IPC_LOCK
      - SYS_RESOURCE
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
